// PlayObject.cpp - 玩家对象实现
// TODO: 实现玩家对象

#include "PlayObject.h"
#include "../Protocol/PacketTypes.h"
#include "../GameEngine/GameEngine.h"
#include "../GameEngine/StorageManager.h"
#include "../GameEngine/PKManager.h"
#include "../GameEngine/GroupManager.h"
#include "../GameEngine/GuildManager.h"
#include "../Common/Logger.h"
#include <algorithm>

namespace MirServer {

// 常量定义
const int MAXBAGITEMS = 46;           // 背包最大物品数
const int MAXSTORAGEITEMCOUNT = 50;   // 仓库最大物品数

PlayObject::PlayObject() : BaseObject() {
    m_humDataInfo = HumDataInfo();
    m_humDataInfo.bagItems.resize(MAXBAGITEMS);
    m_humDataInfo.storageItems.resize(MAXSTORAGEITEMCOUNT);

    // 初始化新成员变量
    m_job = JobType::WARRIOR;
    m_gender = GenderType::MALE;
    m_connectionId = 0;
    m_lastActiveTime = GetCurrentTime();

    // 初始化角色基本信息
    m_humDataInfo.level = 1;
    m_humDataInfo.isDead = false;

    // 初始化基础属性
    InitializeDefaultAbility(m_humDataInfo.abil, m_humDataInfo.job, m_humDataInfo.level);

    // 设置基类属性
    m_hp = m_humDataInfo.abil.HP;
    m_maxHP = m_humDataInfo.abil.MaxHP;
    m_viewRange = 12; // 玩家视野范围更大
}

PlayObject::~PlayObject() {
    LogoutGame();
}

void PlayObject::Initialize() {
    // 初始化玩家特有的功能
    m_lastViewUpdateTime = GetCurrentTime();

    // 发送初始化信息给客户端
    LoginGame();
}

void PlayObject::Finalize() {
    // 保存数据
    SaveData();

    // 清理组队信息
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        // 通知组队管理器玩家下线
        groupManager.OnPlayerLogout(this);

        // 如果是队长，需要特殊处理
        if (groupManager.IsGroupLeader(this)) {
            auto group = groupManager.GetPlayerGroup(this);
            if (group && group->members.size() > 1) {
                // 转移队长给下一个成员
                for (auto& member : group->members) {
                    if (member && member.get() != this && member->IsOnline()) {
                        groupManager.ChangeLeader(this, member.get());
                        break;
                    }
                }
            }
        }

        // 离开组队
        groupManager.LeaveGroup(this);
    }

    // 清理行会信息
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        // 通知行会管理器玩家下线
        guild->OnPlayerLogout(this);
    }

    // 清理交易信息
    if (m_dealPartner) {
        CancelDeal();
    }
}

void PlayObject::Run() {
    // 处理客户端消息
    ProcessMessages();

    // 更新视野
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastViewUpdateTime > 500) { // 每500ms更新一次视野
        UpdateViewMap();
        m_lastViewUpdateTime = currentTime;
    }

    // 检查升级
    CheckLevelUp();
}

bool PlayObject::Walk(DirectionType dir) {
    Point nextPos = GetNextPosition(dir);

    // 先转向
    TurnTo(dir);

    // 检查是否可以移动
    if (!CanMove(nextPos)) {
        SendDefMessage(Protocol::SM_MOVEFAIL, 0, m_currentPos.x, m_currentPos.y, 0);
        return false;
    }

    // 执行移动
    Point oldPos = m_currentPos;
    m_currentPos = nextPos;
    OnPositionChanged();

    // 发送移动成功消息
    SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(dir));

    return true;
}

void PlayObject::SendMessage(const std::string& msg, BYTE color) {
    // TODO: 实现发送文本消息到客户端
}

void PlayObject::SendDefMessage(WORD msgType, WORD recog, WORD param, WORD tag, WORD series) {
    // 添加消息到队列
    Message msg;
    msg.msgType = msgType;
    msg.recog = recog;
    msg.param = param;
    msg.tag = tag;
    msg.series = series;
    m_messageQueue.push_back(msg);
}

void PlayObject::RecalcAbility() {
    // 基础属性重置
    InitializeDefaultAbility(m_humDataInfo.abil, m_humDataInfo.job, m_humDataInfo.level);

    // 计算装备加成
    for (size_t i = 0; i < static_cast<size_t>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem& item = m_humDataInfo.useItems[i];
        if (item.itemIndex > 0) {
            // TODO: 根据物品属性增加能力值
            // 这里需要物品数据库支持
        }
    }

    // 更新基类属性
    m_hp = std::min(m_hp, m_humDataInfo.abil.MaxHP);
    m_maxHP = m_humDataInfo.abil.MaxHP;

    // 通知客户端
    SendAbility();
}

bool PlayObject::TakeOnItem(const UserItem& item, EquipPosition pos) {
    if (pos >= EquipPosition::MAX_EQUIP) return false;

    // 检查是否已有装备
    UserItem& equipSlot = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    if (equipSlot.itemIndex > 0) {
        // 需要先卸下原装备
        return false;
    }

    // 装备物品
    equipSlot = item;

    // 重新计算属性
    RecalcAbility();

    // 通知客户端
    SendDefMessage(Protocol::SM_TAKEONITEM, item.makeIndex, static_cast<WORD>(pos), 0, 0);

    return true;
}

bool PlayObject::TakeOffItem(EquipPosition pos, UserItem& outItem) {
    if (pos >= EquipPosition::MAX_EQUIP) return false;

    UserItem& equipSlot = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    if (equipSlot.itemIndex == 0) {
        return false; // 没有装备
    }

    // 检查背包是否有空间
    if (IsBagFull()) {
        return false;
    }

    // 卸下装备
    outItem = equipSlot;
    equipSlot = UserItem(); // 清空装备槽

    // 添加到背包
    AddBagItem(outItem);

    // 重新计算属性
    RecalcAbility();

    // 通知客户端
    SendDefMessage(Protocol::SM_TAKEOFFITEM, outItem.makeIndex, static_cast<WORD>(pos), 0, 0);

    return true;
}

bool PlayObject::AddBagItem(const UserItem& item) {
    if (IsBagFull()) return false;

    m_humDataInfo.bagItems.push_back(item);

    // 通知客户端
    SendDefMessage(Protocol::SM_ADDITEM, item.makeIndex, item.itemIndex, item.dura, 0);

    return true;
}

bool PlayObject::DeleteBagItem(WORD makeIndex) {
    auto it = std::find_if(m_humDataInfo.bagItems.begin(),
                          m_humDataInfo.bagItems.end(),
                          [makeIndex](const UserItem& item) {
                              return item.makeIndex == makeIndex;
                          });

    if (it != m_humDataInfo.bagItems.end()) {
        m_humDataInfo.bagItems.erase(it);

        // 通知客户端
        SendDefMessage(Protocol::SM_DELITEM, makeIndex, 0, 0, 0);
        return true;
    }

    return false;
}

bool PlayObject::IncGold(DWORD amount) {
    if (m_humDataInfo.gold + amount > m_humDataInfo.goldMax) {
        return false;
    }

    m_humDataInfo.gold += amount;
    SendGoldChanged();
    return true;
}

bool PlayObject::DecGold(DWORD amount) {
    if (m_humDataInfo.gold < amount) {
        return false;
    }

    m_humDataInfo.gold -= amount;
    SendGoldChanged();
    return true;
}

void PlayObject::GainExp(DWORD exp) {
    if (exp == 0) return;

    // 检查是否在组队中，如果是则通过组队管理器分配经验
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        // 使用组队经验分享系统
        groupManager.ShareExperience(this, exp);
    } else {
        // 单人获得经验
        GainExpDirect(exp);
    }
}

void PlayObject::GainExpDirect(DWORD exp) {
    if (exp == 0) return;

    // 直接增加经验，不通过组队分享
    m_humDataInfo.abil.Exp += exp;
    SendExpChanged();

    // 检查是否可以升级
    CheckLevelUp();
}

bool PlayObject::CheckLevelUp() {
    bool leveledUp = false;

    while (m_humDataInfo.abil.Exp >= m_humDataInfo.abil.MaxExp &&
           m_humDataInfo.level < MAXLEVEL) {
        // 升级
        m_humDataInfo.abil.Exp -= m_humDataInfo.abil.MaxExp;
        m_humDataInfo.level++;
        m_humDataInfo.abil.Level = m_humDataInfo.level;

        // 重新计算属性
        RecalcAbility();

        leveledUp = true;
    }

    if (leveledUp) {
        SendLevelUp();
    }

    return leveledUp;
}

bool PlayObject::IsAttackTarget(const BaseObject* target) const {
    if (!target || target == this) return false;

    // 如果目标是玩家，使用PK系统判断
    if (target->GetObjectType() == ObjectType::HUMAN) {
        const PlayObject* targetPlayer = static_cast<const PlayObject*>(target);

        // 使用PK管理器判断是否可以攻击
        auto& pkManager = PKManager::GetInstance();
        return pkManager.CanAttack(const_cast<PlayObject*>(this), const_cast<PlayObject*>(targetPlayer));
    }

    // 对于怪物，根据攻击模式判断
    if (target->GetObjectType() == ObjectType::MONSTER) {
        return m_attackMode != AttackMode::PEACE;
    }

    return false;
}

bool PlayObject::IsProperTarget(const BaseObject* target) const {
    return IsAttackTarget(target) && CanSee(target);
}

bool PlayObject::IsProperFriend(const BaseObject* target) const {
    if (!target || target == this) return false;

    if (target->GetObjectType() == ObjectType::HUMAN) {
        const PlayObject* player = static_cast<const PlayObject*>(target);

        // 组队成员是朋友
        if (IsGroupMember(player)) return true;

        // 行会成员是朋友（非敌对状态）
        if (IsGuildMember(player)) return true;

        // 行会联盟成员是朋友
        if (IsGuildAlly(player)) return true;

        // TODO: 夫妻、师徒等关系判断
        // 这些关系需要额外的系统支持，暂时不实现
    }

    return false;
}

void PlayObject::Die() {
    BaseObject::Die();

    // 设置死亡状态
    m_humDataInfo.isDead = true;

    // 通知PK管理器玩家死亡
    auto& pkManager = PKManager::GetInstance();
    pkManager.OnPlayerDeath(this, m_lastAttacker);

    // 掉落物品（TODO）

    // 通知客户端
    SendDefMessage(Protocol::SM_NOWDEATH, 0, m_currentPos.x, m_currentPos.y, 0);
}

void PlayObject::Revive() {
    BaseObject::Revive();

    // 设置复活状态
    m_humDataInfo.isDead = false;

    // 恢复部分生命值和魔法值
    m_humDataInfo.abil.HP = m_humDataInfo.abil.MaxHP / 2;
    m_humDataInfo.abil.MP = m_humDataInfo.abil.MaxMP / 2;
    m_hp = m_humDataInfo.abil.HP;

    // 通知客户端
    SendAbility();
    SendDefMessage(Protocol::SM_ALIVE, 0, 0, 0, 0);
}

void PlayObject::ReviveAtHome() {
    // 传送到回城点
    SpaceMove(m_humDataInfo.homeMap, m_humDataInfo.homePos.x, m_humDataInfo.homePos.y);

    // 复活
    Revive();
}

void PlayObject::LoginGame() {
    // 发送登录成功消息
    SendDefMessage(Protocol::SM_LOGON, 0, m_currentPos.x, m_currentPos.y, static_cast<WORD>(m_direction));

    // 发送地图信息
    SendMapInfo();

    // 发送角色属性
    SendAbility();

    // 发送背包物品
    SendBagItems();

    // 发送魔法列表
    SendMagics();

    // 通知组队管理器玩家上线
    auto& groupManager = GroupManager::GetInstance();
    groupManager.OnPlayerLogin(this);

    // 通知行会管理器玩家上线
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        guild->OnPlayerLogin(this);
    }
}

void PlayObject::SendMapInfo() {
    // TODO: 发送地图描述信息
    SendDefMessage(Protocol::SM_MAPDESCRIPTION, 0, 0, 0, 0);
}

void PlayObject::SendAbility() {
    // TODO: 打包能力值数据发送
    SendDefMessage(Protocol::SM_ABILITY, 0, 0, 0, 0);
}

void PlayObject::SendBagItems() {
    // TODO: 打包背包物品数据发送
    SendDefMessage(Protocol::SM_BAGITEMS, 0, 0, 0, 0);
}

void PlayObject::SendMagics() {
    // TODO: 打包魔法列表发送
    SendDefMessage(Protocol::SM_SENDMYMAGIC, 0, 0, 0, 0);
}

void PlayObject::SendStorageItems(const std::vector<UserItem>& items, DWORD gold) {
    // TODO: 实现发送仓库物品列表到客户端
    // 这里应该将仓库物品数据打包发送

    // 发送仓库金币
    SendDefMessage(Protocol::SM_SAVEITEMLIST, 0,
                  static_cast<WORD>(gold & 0xFFFF),
                  static_cast<WORD>(gold >> 16), 0);

    // 发送每个物品
    for (const auto& item : items) {
        // TODO: 发送具体的物品数据包
        SendDefMessage(Protocol::SM_ADDITEM, item.makeIndex, 0, 0, 0);
    }
}

void PlayObject::SendGoldChanged() {
    SendDefMessage(Protocol::SM_GOLDCHANGED, 0,
                  static_cast<WORD>(m_humDataInfo.gold & 0xFFFF),
                  static_cast<WORD>(m_humDataInfo.gold >> 16), 0);
}

void PlayObject::SendExpChanged() {
    // TODO: 发送经验值变化
}

void PlayObject::SendLevelUp() {
    // TODO: 发送升级效果
}

void PlayObject::SendHealthChanged() {
    SendDefMessage(Protocol::SM_HEALTHSPELLCHANGED, 0,
                  m_humDataInfo.abil.HP, m_humDataInfo.abil.MP, 0);
}

void PlayObject::Say(const std::string& msg) {
    // TODO: 实现说话功能
    SendDefMessage(Protocol::SM_SAY, 0, 0, 0, 0);
}

void PlayObject::CancelDeal() {
    if (m_dealPartner) {
        m_dealPartner->SetDealPartner(nullptr);
        m_dealPartner = nullptr;
    }

    m_dealItems.clear();
    m_dealGold = 0;
    m_dealLocked = false;

    SendDefMessage(Protocol::SM_DEALCANCEL, 0, 0, 0, 0);
}

void PlayObject::IncPKPoint(int value) {
    m_humDataInfo.pkPoint += value;
    if (m_humDataInfo.pkPoint < 0) {
        m_humDataInfo.pkPoint = 0;
    }

    // 使用PK管理器更新状态
    auto& pkManager = PKManager::GetInstance();

    // PK值影响名称颜色
    if (pkManager.IsRedName(this)) {
        SetNameColor(12); // 红名
    } else if (pkManager.IsYellowName(this)) {
        SetNameColor(11); // 黄名
    } else {
        SetNameColor(0); // 白名
    }
}

bool PlayObject::IsGroupMember(const PlayObject* player) const {
    if (!player) return false;

    // 使用组队管理器检查是否是组队成员
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsGroupMember(const_cast<PlayObject*>(this), const_cast<PlayObject*>(player));
}

bool PlayObject::IsInGroup() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsInGroup(const_cast<PlayObject*>(this));
}

bool PlayObject::IsGroupLeader() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsGroupLeader(const_cast<PlayObject*>(this));
}

std::vector<std::shared_ptr<PlayObject>> PlayObject::GetGroupMembers() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.GetGroupMembers(const_cast<PlayObject*>(this));
}

void PlayObject::ProcessMessages() {
    // TODO: 处理消息队列中的消息
    while (!m_messageQueue.empty()) {
        const Message& msg = m_messageQueue.front();
        // TODO: 发送到客户端
        m_messageQueue.pop_front();
    }
}

void PlayObject::UpdateViewMap() {
    // TODO: 更新视野内的对象列表
    GetViewObjects(m_viewList);
}

void PlayObject::OnPositionChanged() {
    // 位置改变时的处理
    UpdateViewMap();
}

void PlayObject::OnDirectionChanged() {
    // 方向改变时的处理
}

void PlayObject::OnStateChanged() {
    // 状态改变时的处理
}

void PlayObject::OnHPChanged() {
    // 生命值改变时的处理
    SendHealthChanged();
}

void PlayObject::SaveData() {
    // TODO: 保存角色数据到数据库
}

void PlayObject::LoadData() {
    // TODO: 从数据库加载角色数据
}

bool PlayObject::UseBagItem(WORD makeIndex) {
    // TODO: 实现使用背包物品
    return false;
}

bool PlayObject::AddMagic(const UserMagic& magic) {
    // TODO: 实现添加魔法
    m_humDataInfo.magics.push_back(magic);
    return true;
}

bool PlayObject::DeleteMagic(WORD magicId) {
    // TODO: 实现删除魔法
    return false;
}

bool PlayObject::UseMagic(WORD magicId, BaseObject* target) {
    // TODO: 实现使用魔法
    return false;
}

void PlayObject::AttackTarget(BaseObject* target) {
    if (!target || !IsAttackTarget(target)) {
        return;
    }

    // 设置目标的最后攻击者
    if (target->GetObjectType() == ObjectType::HUMAN) {
        PlayObject* targetPlayer = static_cast<PlayObject*>(target);
        targetPlayer->SetLastAttacker(this);

        // 如果杀死了目标，通知PK管理器
        if (target->GetHP() <= 0) {
            auto& pkManager = PKManager::GetInstance();
            pkManager.OnPlayerKill(this, targetPlayer);
        }
    }

    // TODO: 实现具体的攻击逻辑
    // 1. 计算伤害
    // 2. 应用伤害
    // 3. 发送攻击效果
}

void PlayObject::BeAttacked(BaseObject* attacker, int damage) {
    if (!attacker || damage <= 0) {
        return;
    }

    // 记录最后攻击者
    SetLastAttacker(attacker);

    // 扣除生命值
    if (damage >= static_cast<int>(m_hp)) {
        m_hp = 0;
        Die();
    } else {
        m_hp -= damage;
        OnHPChanged();
    }

    // TODO: 实现具体的被攻击逻辑
    // 1. 播放被攻击效果
    // 2. 发送伤害数值
    // 3. 处理反击逻辑
}

const UserItem* PlayObject::GetEquipItem(EquipPosition pos) const {
    if (pos >= EquipPosition::MAX_EQUIP) return nullptr;
    const UserItem& item = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    return (item.itemIndex > 0) ? &item : nullptr;
}

void PlayObject::Whisper(const std::string& targetName, const std::string& msg) {
    // TODO: 实现私聊
}

void PlayObject::GroupMsg(const std::string& msg) {
    // 使用组队管理器发送组队消息
    auto& groupManager = GroupManager::GetInstance();
    groupManager.SendGroupMessage(this, msg);
}

void PlayObject::GuildMsg(const std::string& msg) {
    // 使用行会管理器发送行会消息
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());

    if (guild) {
        guild->SendGuildMessage(GetCharName() + ": " + msg);
    } else {
        SendMessage("您不在任何行会中", 0);
    }
}

bool PlayObject::IsGuildMember(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    return m_humDataInfo.guildName == player->GetGuildName();
}

bool PlayObject::IsGuildAlly(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    // 如果是同一个行会，不算联盟
    if (IsGuildMember(player)) {
        return false;
    }

    auto& guildManager = GuildManager::GetInstance();
    auto* myGuild = guildManager.FindGuild(m_humDataInfo.guildName);
    auto* playerGuild = guildManager.FindGuild(player->GetGuildName());

    if (myGuild && playerGuild) {
        return myGuild->IsAlly(playerGuild);
    }

    return false;
}

bool PlayObject::IsGuildEnemy(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    // 如果是同一个行会或联盟，不是敌人
    if (IsGuildMember(player) || IsGuildAlly(player)) {
        return false;
    }

    auto& guildManager = GuildManager::GetInstance();
    auto* myGuild = guildManager.FindGuild(m_humDataInfo.guildName);
    auto* playerGuild = guildManager.FindGuild(player->GetGuildName());

    if (myGuild && playerGuild) {
        return myGuild->IsWarWith(playerGuild);
    }

    return false;
}

void PlayObject::SetGuildInfo(const std::string& guildName, BYTE rank) {
    m_humDataInfo.guildName = guildName;
    m_humDataInfo.guildRank = rank;

    // 通知客户端行会信息变化
    SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

    Logger::Debug("Player " + GetCharName() + " guild info updated: " + guildName + " rank " + std::to_string(rank));
}

void PlayObject::ClearGuildInfo() {
    m_humDataInfo.guildName.clear();
    m_humDataInfo.guildRank = 0;

    // 通知客户端行会信息变化
    SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

    Logger::Debug("Player " + GetCharName() + " guild info cleared");
}

// procedure TBaseObject.RefShowName();
// begin
//   SendRefMsg(RM_USERNAME, 0, 0, 0, 0, GetShowName);
// end;
void PlayObject::LogoutGame() {
    // 通知组队管理器玩家下线
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        groupManager.OnPlayerLogout(this);
    }

    // 通知行会管理器玩家下线
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        guild->OnPlayerLogout(this);
    }

    // 保存数据
    SaveData();
}

bool PlayObject::OpenStorage(const std::string& password) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendMessage("仓库系统暂时不可用", 0);
        return false;
    }

    // 尝试打开仓库
    if (storageManager->OpenStorage(this, password)) {
        // 发送仓库物品列表给客户端
        const auto& items = storageManager->GetStorageItems(this);
        DWORD gold = storageManager->GetStorageGold(this);

        // 发送仓库开启成功消息
        SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);

        // 发送仓库物品列表
        SendStorageItems(items, gold);

        SendMessage("仓库已打开", 0);
        return true;
    } else {
        SendDefMessage(Protocol::SM_STORAGEPASSWORD_FAIL, 0, 0, 0, 0);
        SendMessage("仓库密码错误", 0);
        return false;
    }
}

bool PlayObject::CloseStorage() {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        return false;
    }

    // 关闭仓库
    if (storageManager->CloseStorage(this)) {
        SendMessage("仓库已关闭", 0);
        return true;
    }

    return false;
}

bool PlayObject::StorageAddItem(const UserItem& item) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        return false;
    }

    // 检查仓库是否打开
    if (!storageManager->IsStorageOpen(this)) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("请先打开仓库", 0);
        return false;
    }

    // 检查仓库是否已满
    if (storageManager->IsStorageFull(this)) {
        SendDefMessage(Protocol::SM_STORAGE_FULL, 0, 0, 0, 0);
        SendMessage("仓库已满", 0);
        return false;
    }

    // 从背包中删除物品
    if (!DeleteBagItem(item.makeIndex)) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("物品不存在", 0);
        return false;
    }

    // 添加到仓库
    if (storageManager->StoreItem(this, item)) {
        SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);
        SendMessage("物品已存入仓库", 0);
        return true;
    } else {
        // 回滚：重新添加到背包
        AddBagItem(item);
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("存储失败", 0);
        return false;
    }
}

bool PlayObject::StorageTakeItem(WORD makeIndex, UserItem& outItem) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        return false;
    }

    // 检查仓库是否打开
    if (!storageManager->IsStorageOpen(this)) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        SendMessage("请先打开仓库", 0);
        return false;
    }

    // 检查背包是否已满
    if (IsBagFull()) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FULLBAG, 0, 0, 0, 0);
        SendMessage("背包已满", 0);
        return false;
    }

    // 从仓库取出物品
    if (storageManager->TakeItem(this, makeIndex, outItem)) {
        // 添加到背包
        if (AddBagItem(outItem)) {
            SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_OK, 0, 0, 0, 0);
            SendMessage("物品已取出", 0);
            return true;
        } else {
            // 回滚：重新添加到仓库
            storageManager->StoreItem(this, outItem);
            SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
            SendMessage("背包空间不足", 0);
            return false;
        }
    } else {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        SendMessage("物品不存在", 0);
        return false;
    }
}

void PlayObject::SetLevel(WORD level) {
    m_humDataInfo.level = level;
    // TODO: 更新属性，发送等级变化通知
}

bool PlayObject::SpaceMove(const std::string& mapName, int x, int y) {
    if (!CanSpaceMove()) {
        return false;
    }

    // 保存当前位置
    std::string oldMapName = GetMapName();
    Point oldPos = GetCurrentPos();

    try {
        // 设置新位置
        m_humDataInfo.mapName = mapName;
        SetCurrentPos(Point{static_cast<WORD>(x), static_cast<WORD>(y)});

        // 发送地图切换消息
        SendDefMessage(Protocol::SM_SPACEMOVE_SHOW, static_cast<WORD>(x), static_cast<WORD>(y), 0, 0);
        SendDefMessage(Protocol::SM_MAPCHANGED, 0, 0, 0, 0);

        // 更新视野
        UpdateViewMap();

        Logger::Info("Player " + GetCharName() + " space moved from " + oldMapName +
                    " to " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")");

        return true;
    } catch (const std::exception& e) {
        // 恢复原位置
        m_humDataInfo.mapName = oldMapName;
        SetCurrentPos(oldPos);

        Logger::Error("SpaceMove failed for player " + GetCharName() + ": " + e.what());
        return false;
    }
}

bool PlayObject::CanSpaceMove() const {
    // 检查是否可以传送
    if (IsDead()) {
        return false;
    }

    // 检查是否在战斗状态
    // TODO: 添加战斗状态检查

    return true;
}

void PlayObject::SetGold(DWORD gold) {
    m_humDataInfo.gold = gold;
    // TODO: 发送金币变化通知
}

void PlayObject::SendPacket(const std::vector<uint8_t>& packet) {
    // TODO: 通过网络管理器发送数据包到客户端
    // 这里需要与NetworkManager集成
    UpdateActiveTime();
}

} // namespace MirServer